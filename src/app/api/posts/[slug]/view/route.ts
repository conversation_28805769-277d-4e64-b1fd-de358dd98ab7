import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";

interface Props {
  params: {
    slug: string;
  };
}

interface JWTPayload {
  userId: string;
  email: string;
  role: string;
}

export async function POST(request: Request, { params }: Props) {
  try {
    const { slug } = params;

    // Lấy thông tin user từ cookie (nếu có)
    const cookieStore = await cookies();
    const accessToken = cookieStore.get('accessToken')?.value;
    
    let userId: string | null = null;
    
    if (accessToken) {
      try {
        const decoded = jwtDecode<JWTPayload>(accessToken);
        userId = decoded.userId;
      } catch (error) {
        console.log('Invalid token, treating as anonymous view');
      }
    }

    // Tìm bài viết
    const post = await prisma.post.findUnique({
      where: { slug },
      select: { id: true, viewedBy: true }
    });

    if (!post) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    // Nếu có userId và chưa xem bài viết này
    if (userId && !post.viewedBy.includes(userId)) {
      await prisma.post.update({
        where: { id: post.id },
        data: {
          viewedBy: [...post.viewedBy, userId]
        }
      });
    } else if (!userId) {
      // Nếu không có userId (anonymous), vẫn tăng view bằng cách thêm một ID tạm thời
      // Sử dụng IP address hoặc session ID để tránh duplicate
      const forwardedFor = request.headers.get('x-forwarded-for');
      const ip = forwardedFor ? forwardedFor.split(',')[0] : 'anonymous';
      const anonymousId = `anonymous_${ip}_${Date.now()}`;

      await prisma.post.update({
        where: { id: post.id },
        data: {
          viewedBy: [...post.viewedBy, anonymousId]
        }
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[POST_VIEW]", error);
    return NextResponse.json(
      { error: "Failed to update view count" },
      { status: 500 }
    );
  }
}
