import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";

interface Props {
  params: {
    slug: string;
  };
}

interface JWTPayload {
  userId: string;
  email: string;
  role: string;
}

export async function POST(request: Request, { params }: Props) {
  try {
    const { slug } = params;
    
    // Lấy thông tin user từ cookie
    const cookieStore = await cookies();
    const accessToken = cookieStore.get('accessToken')?.value;
    
    if (!accessToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    let userId: string;
    try {
      const decoded = jwtDecode<JWTPayload>(accessToken);
      userId = decoded.userId;
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      );
    }

    // Tìm bài viết
    const post = await prisma.post.findUnique({
      where: { slug },
      select: { id: true, likedBy: true }
    });

    if (!post) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    const isLiked = post.likedBy.includes(userId);
    
    // Toggle like status
    const updatedPost = await prisma.post.update({
      where: { id: post.id },
      data: {
        likedBy: isLiked 
          ? post.likedBy.filter(id => id !== userId) // Remove like
          : [...post.likedBy, userId] // Add like
      },
      select: {
        likedBy: true
      }
    });

    return NextResponse.json({ 
      success: true, 
      isLiked: !isLiked,
      likesCount: updatedPost.likedBy.length
    });
  } catch (error) {
    console.error("[POST_LIKE]", error);
    return NextResponse.json(
      { error: "Failed to update like status" },
      { status: 500 }
    );
  }
}
