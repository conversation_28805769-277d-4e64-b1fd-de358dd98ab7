@tailwind base;
@tailwind components;
@tailwind utilities;

/* Admin layout styles */
body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
  background-color: #f3f4f6 !important;
}

/* Override container styles for admin pages */
body > main > div.container {
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Hide header and footer on admin pages */
body > header,
body > footer {
  display: none !important;
}

/* Ensure single scrollbar for admin pages */
body > main {
  min-height: auto !important;
  overflow: visible !important;
}
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
/* Tùy chỉnh thanh cuộn */
::-webkit-scrollbar {
  width: 10px; /* Độ rộng của thanh cuộn */
}

::-webkit-scrollbar-track {
  background: #e0e0e0; /* Màu nền của track thanh cuộn */
  border-radius: 10px; /* Bo tròn góc */
}

::-webkit-scrollbar-thumb {
  background-color: #3498db; /* Màu của thanh kéo */
  border-radius: 10px; /* Bo tròn góc thanh kéo */
  border: 2px solid #e0e0e0; /* Viền quanh thanh kéo để tạo hiệu ứng nổi */
}

::-webkit-scrollbar-thumb:hover {
  background-color: #2980b9; /* Màu khi hover */
}

/* Tùy chỉnh thanh cuộn cho Firefox */
body {
  scrollbar-color: #3498db #e0e0e0; /* Màu của thanh kéo và màu nền */
  scrollbar-width: thin; /* Độ rộng thanh cuộn */
}
