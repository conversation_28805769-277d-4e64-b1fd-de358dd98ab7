{"name": "blog", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:studio": "prisma studio", "check-mongodb": "node scripts/check-mongodb.js", "seed": "ts-node prisma/seed.ts"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@shadcn/ui": "^0.0.4", "@supabase/supabase-js": "^2.47.14", "@tanstack/react-query": "^5.64.1", "@tanstack/react-query-devtools": "^5.64.1", "@tinymce/tinymce-react": "^5.1.1", "@types/js-cookie": "^3.0.6", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "framer-motion": "^11.11.11", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.454.0", "mongodb": "^6.16.0", "next": "15.0.2", "next-auth": "^4.24.11", "prismjs": "^1.29.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.54.2", "react-typed": "^2.0.12", "slugify": "^1.6.6", "sonner": "^1.7.2", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.1", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/prismjs": "^1.26.5", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8", "eslint-config-next": "15.0.2", "postcss": "^8", "prisma": "^6.8.2", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}}