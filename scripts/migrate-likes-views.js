const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateLikesAndViews() {
  try {
    console.log('Starting migration of likes and views...');
    
    // Get all posts
    const posts = await prisma.post.findMany({
      select: {
        id: true,
        title: true,
      }
    });

    console.log(`Found ${posts.length} posts to migrate`);

    // Update each post to initialize empty arrays
    for (const post of posts) {
      await prisma.post.update({
        where: { id: post.id },
        data: {
          viewedBy: [],
          likedBy: [],
        }
      });
      console.log(`Migrated post: ${post.title}`);
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateLikesAndViews();
