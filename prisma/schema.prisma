// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

// Model User để quản lý người dùng và xác thực
model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String
  email         String    @unique
  password      String
  role          Role      @default(USER)
  avatar        String?
  bio           String?
  dob           DateTime?
  posts         Post[]    // Thêm relation với Post
  sessions      Session[] // Thêm relation với Session
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  @@map("users")
}

enum Role {
  USER
  ADMIN
}

model Post {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  slug        String    @unique
  content     String
  excerpt     String
  coverImage  String?   @map("cover_image")
  published   <PERSON><PERSON>an   @default(false) @map("published")
  is<PERSON>inn<PERSON>   @default(false) @map("is_pinned")
  viewedBy    String[]  @default([]) @map("viewed_by")
  likedBy     String[]  @default([]) @map("liked_by")
  authorId    String    @db.ObjectId @map("author_id")
  author      User      @relation(fields: [authorId], references: [id])
  tags        String[]
  series      Series?   @relation(fields: [seriesId], references: [id])
  seriesId    String?   @db.ObjectId @map("series_id")
  orderInSeries Int?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  @@map("posts")
}

model Project {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  title         String
  slug          String    @unique
  excerpt       String
  description   String
  thumbnail     String
  techStack     String[]  @map("tech_stack")
  status        Boolean   @default(false) @map("status")
  views         Int       @default(0) @map("views")
  likes         Int       @default(0) @map("likes")
  isPinned      Boolean   @default(false) @map("is_pinned")
  isHidden      Boolean   @default(false) @map("is_hidden")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  @@map("projects")
}

model Series {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  title       String    @map("title")
  slug        String    @unique
  description String    @map("description")
  coverImage  String?   @map("cover_image")
  isActive    Boolean   @default(true) @map("is_active")
  posts       Post[]
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  @@map("series")
}

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  userId       String   @db.ObjectId @map("user_id")
  refreshToken String   @map("refresh_token")
  userAgent    String?  @map("user_agent")
  ipAddress    String?  @map("ip_address")
  isValid      Boolean  @default(true) @map("is_valid")
  expiresAt    DateTime @map("expires_at")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  user         User     @relation(fields: [userId], references: [id])

  @@map("sessions")
  @@index([expiresAt])
}

model Profile {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String
  title         String
  bio           String
  avatar        String
  email         String
  location      String
  skills        Skill[]
  socialLinks   SocialLink[]
  education     Education[]
  experience    Experience[]
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  @@map("profiles")
}

model Skill {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  category    String    // e.g., "Frontend", "Backend", "Database", "DevOps"
  level       Int       // 1-5 scale
  profileId   String    @db.ObjectId @map("profile_id")
  profile     Profile   @relation(fields: [profileId], references: [id])
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  @@map("skills")
}

model SocialLink {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  platform    String    // e.g., "GitHub", "LinkedIn", "Twitter"
  url         String
  profileId   String    @db.ObjectId @map("profile_id")
  profile     Profile   @relation(fields: [profileId], references: [id])
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  @@map("social_links")
}

model Education {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  school      String
  degree      String
  field       String
  startDate   DateTime  @map("start_date")
  endDate     DateTime? @map("end_date")
  description String?
  profileId   String    @db.ObjectId @map("profile_id")
  profile     Profile   @relation(fields: [profileId], references: [id])
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  @@map("education")
}

model Experience {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  company     String
  position    String
  startDate   DateTime  @map("start_date")
  endDate     DateTime? @map("end_date")
  description String
  profileId   String    @db.ObjectId @map("profile_id")
  profile     Profile   @relation(fields: [profileId], references: [id])
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  @@map("experience")
}